package org.jeecg.modules.cw.mnlr.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.DateRange;
import cn.hutool.core.date.DateField;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrStatisticsDay;
import org.jeecg.modules.cw.mnlr.mapper.CwMnlrStatisticsDayMapper;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;
import org.jeecg.modules.cw.mnlr.result.CwMnlrDayQueryResult;
import org.jeecg.modules.cw.mnlr.util.MnlrStatisticsUtil;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.jeecg.modules.cw.base.service.ICwKBaseService;
import org.springframework.context.annotation.Lazy;

/**
 * @Description: 矿模拟利润统计数据（日）
 * @Author: jeecg-boot
 * @Date:   2025-02-20
 * @Version: V1.0
 */
@Log4j2
@Service
public class CwMnlrStatisticsDayServiceImpl extends ServiceImpl<CwMnlrStatisticsDayMapper, CwMnlrStatisticsDay> implements ICwMnlrStatisticsDayService {

    @Resource
    private ICwMnlrDayService mnlrDayService;

    @Lazy
    @Resource
    private ICwKBaseService kBaseService;

    @Override
    public void addOrEdit(CwMnlrStatisticsDay entity) {
        if (ObjectUtil.isEmpty(entity.getRecordTime())) {
            return;
        }
        List<CwMnlrStatisticsDay> list =
                this.lambdaQuery().eq(CwMnlrStatisticsDay::getRecordTime, entity.getRecordTime()).list();
        if (list.isEmpty()) {
            this.save(entity);
        } else {
            this.lambdaUpdate().eq(CwMnlrStatisticsDay::getRecordTime, entity.getRecordTime())
                    .setEntity(entity);
        }
    }

    @Override
    public BigDecimal getDayProfit(Date date) {
        if (date == null) {
            return BigDecimal.ZERO;
        }
        CwMnlrStatisticsDay one = this.lambdaQuery()
                .eq(CwMnlrStatisticsDay::getRecordTime, DateUtil.beginOfDay(date))
                .one();
        return one != null && one.getMnlr() != null ? one.getMnlr() : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal sumMnlrRange(Date start, Date end) {
        if (start == null || end == null || start.after(end)) {
            return BigDecimal.ZERO;
        }
        // 一次性查出区间内所有日统计，内存累加，避免多次数据库访问
        return this.lambdaQuery()
                .ge(CwMnlrStatisticsDay::getRecordTime, DateUtil.beginOfDay(start))
                .le(CwMnlrStatisticsDay::getRecordTime, DateUtil.endOfDay(end))
                .list()
                .stream()
                .map(CwMnlrStatisticsDay::getMnlr)
                .filter(ObjectUtil::isNotNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal sumSlRange(Date start, Date end) {
        if (start == null || end == null || start.after(end)) {
            return BigDecimal.ZERO;
        }
        // 一次性查出区间内所有日统计，内存累加销售收入
        return this.lambdaQuery()
                .ge(CwMnlrStatisticsDay::getRecordTime, DateUtil.beginOfDay(start))
                .le(CwMnlrStatisticsDay::getRecordTime, DateUtil.endOfDay(end))
                .list()
                .stream()
                .map(item -> {
                    String sl = item.getSl();
                    if (ObjectUtil.isNotEmpty(sl)) {
                        try {
                            return new BigDecimal(sl);
                        } catch (NumberFormatException e) {
                            return BigDecimal.ZERO;
                        }
                    }
                    return BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public void recalcDay(Date date) {
        if (date == null) {
            log.warn("重算日期为空，跳过重算");
            return;
        }
        Date day = DateUtil.beginOfDay(date);
        log.info("开始重算日期: {}", DateUtil.formatDate(day));
        
        CwMnlrDayQueryResult result = mnlrDayService.queryByDate(day);

        if (result == null || result.getRows() == null || result.getRows().isEmpty()) {
            log.info("日期 {} 无数据，删除原有统计记录", DateUtil.formatDate(day));
            // 若无数据，则删除原有统计记录
            this.lambdaUpdate().eq(CwMnlrStatisticsDay::getRecordTime, day).remove();
            return;
        }
        
        log.info("日期 {} 查询到 {} 条记录", DateUtil.formatDate(day), result.getRows().size());
        
        CwMnlrStatisticsDay statisticsDay = MnlrStatisticsUtil.calculateStatistics(result, day);
        if (statisticsDay != null) {
            // 使用新的总成本逻辑  ？？？？ 成本？？？？不一样
            BigDecimal cbSum = kBaseService.getTotalDrs(day);
            statisticsDay.setCb(cbSum);
            log.debug("日期 {} 总成本: {}", DateUtil.formatDate(day), cbSum);

            // 重新计算 合计模拟利润(mnlr) 与 jhb，并填充 baseMnlr
            BigDecimal sl = new BigDecimal(statisticsDay.getSl());
            BigDecimal qtfy = statisticsDay.getQt();
            BigDecimal djlr = statisticsDay.getDjlr();
            BigDecimal gsjh = statisticsDay.getGsjh();

            BigDecimal baseSimProfit = sl.subtract(cbSum).subtract(qtfy);
            BigDecimal mnlr = baseSimProfit.add(djlr); // 合计模拟利润
            BigDecimal jhb = mnlr.subtract(gsjh);

            statisticsDay.setBaseMnlr(baseSimProfit);
            statisticsDay.setMnlr(mnlr);
            statisticsDay.setJhb(jhb);

            log.debug("日期 {} 计算结果 - 销售收入: {}, 其他费用: {}, 单价利润: {}, 公司计划: {}, 基础模拟利润: {}, 合计模拟利润: {}, 计划表: {}", 
                DateUtil.formatDate(day), sl, qtfy, djlr, gsjh, baseSimProfit, mnlr, jhb);

            CwMnlrStatisticsDay existing = this.lambdaQuery()
                    .eq(CwMnlrStatisticsDay::getRecordTime, day)
                    .one();
            if (existing == null) {
                this.save(statisticsDay);
                log.info("日期 {} 新增统计记录", DateUtil.formatDate(day));
            } else {
                statisticsDay.setId(existing.getId());
                this.updateById(statisticsDay);
                log.info("日期 {} 更新统计记录", DateUtil.formatDate(day));
            }
        } else {
            log.warn("日期 {} 计算统计结果为空", DateUtil.formatDate(day));
        }
    }

    @Override
    public void recalcMonth(int year, int month) {
        // 月份参数: 1-12
        if (month < 1 || month > 12) {
            log.error("月份参数错误: {}, 必须是1-12", month);
            throw new IllegalArgumentException("month must be 1-12");
        }
        Date firstDay = DateUtil.parse(String.format("%d-%02d-01", year, month));
        Date lastDay = DateUtil.endOfMonth(firstDay);
        log.info("开始重算月份: {}-{}, 日期范围: {} 至 {}", year, month, DateUtil.formatDate(firstDay), DateUtil.formatDate(lastDay));
        
        DateRange range = DateUtil.range(firstDay, lastDay, DateField.DAY_OF_MONTH);
        int totalDays = 0;
        for (Date day : range) {
            recalcDay(day);
            totalDays++;
        }
        log.info("月份 {}-{} 重算完成，共处理 {} 天", year, month, totalDays);
    }

    @Override
    public void recalcAll() {
        log.info("开始全量重算模拟利润统计数据");
        // 查出所有已存在的 CwMnlrDay 记录日期
        List<Date> recordDates = mnlrDayService.lambdaQuery()
                .select(CwMnlrDay::getRecordTime)
                .list()
                .stream()
                .map(CwMnlrDay::getRecordTime)
                .map(DateUtil::beginOfDay)
                .collect(Collectors.toList());
        // 使用 Set 去重
        Set<Date> uniqueDates = new HashSet<>(recordDates);
        log.info("查询到 {} 条原始记录，去重后 {} 个唯一日期", recordDates.size(), uniqueDates.size());
        
        int processedCount = 0;
        for (Date date : uniqueDates) {
            recalcDay(date);
            processedCount++;
            if (processedCount % 10 == 0) {
                log.info("全量重算进度: {}/{}", processedCount, uniqueDates.size());
            }
        }
        log.info("全量重算完成，共处理 {} 个日期", processedCount);
    }

    @Override
    public void recalcRange(Date start, Date end) {
        if (start == null || end == null) {
            log.warn("重算范围参数为空，开始日期: {}, 结束日期: {}", start, end);
            return;
        }
        Date begin = DateUtil.beginOfDay(start);
        Date finish = DateUtil.beginOfDay(end);
        log.info("开始重算日期范围: {} 至 {}", DateUtil.formatDate(begin), DateUtil.formatDate(finish));
        
        int processedCount = 0;
        for (Date d = begin; !d.after(finish); d = DateUtil.offsetDay(d, 1)) {
            recalcDay(d);
            processedCount++;
            if (processedCount % 5 == 0) {
                log.info("范围重算进度: 已处理 {} 天", processedCount);
            }
        }
        log.info("范围重算完成，共处理 {} 天", processedCount);
    }
}
